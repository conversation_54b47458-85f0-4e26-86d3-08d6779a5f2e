package dlt698

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"tp.service/internal/common"
)

// Client 表示DL/T 698.45协议客户端
type Client struct {
	ttlPort *common.SerialPort
	builder *Builder
	parser  *Parser
	mutex   sync.Mutex
	timeout time.Duration
}

// NewClient 创建一个新的客户端
func NewClient(config *common.SerialConfig, serverAddr []byte, logicalAddr byte) *Client {
	return &Client{
		ttlPort: common.NewSerial(config),
		builder: NewBuilder(SAddrTypeSingle, SAddrLogical0, serverAddr, logicalAddr),
		parser:  NewParser(),
		timeout: time.Second * 3, // 默认超时3秒
	}
}

// SetTimeout 设置超时时间
func (c *Client) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
}

// Connect 连接设备
func (c *Client) Connect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 打开串口
	if err := c.ttlPort.Connect(); err != nil {
		return err
	}

	// 清空缓冲区
	// if err := c.ttlPort.Flush(); err != nil {
	// 	return err
	// }

	// 重置解析器
	c.parser.Reset()
	return nil
}

// Disconnect 断开连接
func (c *Client) Disconnect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.ttlPort.IsConnected() {
		return nil
	}

	// 关闭串口
	return c.ttlPort.Disconnect()
}

// GetData 获取数据
func (c *Client) GetData(ois []uint16) (map[string]interface{}, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.ttlPort.IsConnected() {
		return nil, errors.New("串口未打开")
	}

	// 清空缓冲区
	// if err := c.ttlPort.Flush(); err != nil {
	// 	return nil, err
	// }

	// 重置解析器
	c.parser.Reset()
	var reqType APDUGetReqType
	if len(ois) <= 0 {
		return nil, errors.New("读取对象属性列表不能为空")
	} else if len(ois) == 1 {
		reqType = APDUGetReqNormal
	} else {
		reqType = APDUGetReqNormalList
	}

	// 构建读取请求
	data, err := c.builder.BuildGetRequest(reqType, ois)
	if err != nil {
		return nil, err
	}

	// 写入数据
	err = c.ttlPort.Send(data, 1)
	if err != nil {
		return nil, err
	}

	// 读取响应
	// response := make([]byte, 1024)
	response, err := c.ttlPort.Receive(c.timeout)
	if err != nil {
		return nil, err
	}
	n := len(response)

	// 解析响应
	c.parser.Append(response[:n])
	frame, err := c.parser.ParseFrame()
	if err != nil {
		return nil, err
	}

	// 检查响应类型
	apdu, err := DecodeAPDU(frame.APDU)
	if err != nil {
		return nil, err
	}

	if apdu.Type != byte(APDUTypeGetResp) {
		return nil, fmt.Errorf("读取响应类型错误，期望0x%02X，实际0x%02X", APDUTypeGetResp, apdu.Type)
	}

	// 解析读取响应
	return ParseGetResponseNormal(apdu)
}

// GetDataWithOADs 获取带OAD的数据
func (c *Client) GetDataWithOADs(oads []*OAD) (map[string]interface{}, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.ttlPort.IsConnected() {
		return nil, errors.New("串口未打开")
	}

	// 清空缓冲区
	// if err := c.ttlPort.Flush(); err != nil {
	// 	return nil, err
	// }

	// 重置解析器
	c.parser.Reset()

	// 构建读取请求
	data, err := c.builder.BuildGetRequestWithOADs(oads)
	if err != nil {
		return nil, err
	}

	// 写入数据
	err = c.ttlPort.Send(data, 1)
	if err != nil {
		return nil, err
	}

	// 读取响应
	// response := make([]byte, 1024)
	response, err := c.ttlPort.Receive(c.timeout)
	if err != nil {
		return nil, err
	}
	n := len(response)

	// 解析响应
	c.parser.Append(response[:n])
	frame, err := c.parser.ParseFrame()
	if err != nil {
		return nil, err
	}

	// 检查响应类型
	apdu, err := DecodeAPDU(frame.APDU)
	if err != nil {
		return nil, err
	}

	if apdu.Type != byte(APDUTypeGetResp) {
		return nil, fmt.Errorf("读取响应类型错误，期望0x%02X，实际0x%02X", APDUTypeGetResp, apdu.Type)
	}

	// 解析读取响应
	return ParseGetResponseNormal(apdu)
}

// SetData 设置数据
func (c *Client) SetData(oi uint16, attrib byte, index byte, value interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.ttlPort.IsConnected() {
		return errors.New("串口未打开")
	}

	// 清空缓冲区
	// if err := c.ttlPort.Flush(); err != nil {
	// 	return err
	// }

	// 重置解析器
	c.parser.Reset()

	// 构建设置请求
	data, err := c.builder.BuildSetRequest(APDUSetReqNormal, []uint16{oi}, []byte{attrib}, []byte{index}, []interface{}{value})
	if err != nil {
		return err
	}

	// 写入数据
	err = c.ttlPort.Send(data, 1)
	if err != nil {
		return err
	}

	// 读取响应
	// response := make([]byte, 1024)
	response, err := c.ttlPort.Receive(c.timeout)
	if err != nil {
		return err
	}
	n := len(response)

	// 解析响应
	c.parser.Append(response[:n])
	frame, err := c.parser.ParseFrame()
	if err != nil {
		return err
	}

	// 检查响应类型
	apdu, err := DecodeAPDU(frame.APDU)
	if err != nil {
		return err
	}

	if apdu.Type != byte(APDUTypeSetResp) {
		return fmt.Errorf("设置响应类型错误，期望0x%02X，实际0x%02X", APDUTypeSetResp, apdu.Type)
	}

	// 解析设置响应
	result, err := ParseSetResponseNormal(apdu)
	if err != nil {
		return err
	}

	// 检查结果
	if result["result"] != "success" {
		return fmt.Errorf("设置失败: %v", result["message"])
	}

	return nil
}

// Action 执行操作
func (c *Client) Action(oi uint16, methodID byte, data interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.ttlPort.IsConnected() {
		return errors.New("串口未打开")
	}

	// 清空缓冲区
	// if err := c.ttlPort.Flush(); err != nil {
	// 	return err
	// }

	// 重置解析器
	c.parser.Reset()

	// 构建操作请求
	reqData, err := c.builder.BuildActionRequest(oi, methodID, data)
	if err != nil {
		return err
	}

	// 写入数据
	err = c.ttlPort.Send(reqData, 1)
	if err != nil {
		return err
	}

	// 读取响应
	// response := make([]byte, 1024)
	response, err := c.ttlPort.Receive(c.timeout)
	if err != nil {
		return err
	}
	n := len(response)

	// 解析响应
	c.parser.Append(response[:n])
	frame, err := c.parser.ParseFrame()
	if err != nil {
		return err
	}

	// 检查响应类型
	apdu, err := DecodeAPDU(frame.APDU)
	if err != nil {
		return err
	}

	if apdu.Type != byte(APDUTypeActionResp) {
		return fmt.Errorf("操作响应类型错误，期望0x%02X，实际0x%02X", APDUTypeActionResp, apdu.Type)
	}

	// 解析操作响应
	result, err := ParseActionResponseNormal(apdu)
	if err != nil {
		return err
	}

	// 检查结果
	if result["result"] != "success" {
		return fmt.Errorf("操作失败: %v", result["message"])
	}

	return nil
}

// IsConnected 检查是否已连接
func (c *Client) IsConnected() bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.ttlPort.IsConnected()
}

// GetSerialPort 获取串口
func (c *Client) GetSerialPort() *common.SerialPort {
	return c.ttlPort
}

// GetBuilder 获取构建器
func (c *Client) GetBuilder() *Builder {
	return c.builder
}

// GetParser 获取解析器
func (c *Client) GetParser() *Parser {
	return c.parser
}
