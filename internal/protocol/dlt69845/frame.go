package dlt698

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
)

// Frame 表示 DL/T 698.45 协议的帧结构
type Frame struct {
	Start   byte   // 帧起始符，固定为 0x68
	Length  uint16 // 长度 L，从控制域开始到校验和之前的长度
	Control byte   // 控制域 C
	Address []byte // 地址域 A
	HCS     uint16 // 帧头校验和
	APDU    []byte // 应用层协议数据单元
	FCS     uint16 // 帧校验和
	End     byte   // 结束符，固定为 0x16
}

func (f *Frame) SetAddress(addrType SAddrType, logicalAddr SAddrLogical, serverAddr []byte, clientAddr byte) {
	sAddrLen := len(serverAddr)
	cAddrLen := 0
	if logicalAddr != SAddrLogicalNone {
		cAddrLen = 1
	}

	f.Address = make([]byte, sAddrLen+1+cAddrLen)
	f.Address[0] = byte(addrType) | byte(logicalAddr) | byte(sAddrLen)
	copy(f.Address[1:], serverAddr)
	if cAddrLen > 0 {
		f.Address[len(f.Address)-1] = clientAddr
	}
}

// NewFrame 创建一个新的帧
func NewFrame(control byte, address []byte, apdu []byte) *Frame {
	return &Frame{
		Start:   FrameFormatNormal,
		Control: control,
		Address: address,
		APDU:    apdu,
		End:     FrameFormatEnd,
	}
}

// Encode 将帧编码为字节数组
func (f *Frame) Encode() ([]byte, error) {
	// 计算地址域长度
	if len(f.Address) == 0 || len(f.Address) > 16 {
		return nil, errors.New("地址域长度无效，应在1-16字节之间")
	}

	// 计算长度域
	// 长度 = 长度域(2) + 控制域(1) + 地址域(N) + HCS(2) + APDU(M) + FCS(2)
	f.Length = uint16(2 + 1 + len(f.Address) + 2 + len(f.APDU) + 2)

	// 创建缓冲区
	buf := new(bytes.Buffer)

	// 写入帧起始符
	buf.WriteByte(f.Start)

	// 写入长度域
	binary.Write(buf, binary.LittleEndian, f.Length)

	// 创建临时缓冲区用于计算HCS
	hcsBuf := new(bytes.Buffer)
	binary.Write(hcsBuf, binary.LittleEndian, f.Length)
	hcsBuf.WriteByte(f.Control)
	hcsBuf.Write(f.Address)

	// 写入控制域
	buf.WriteByte(f.Control)

	// 写入地址域
	buf.Write(f.Address)

	// 计算帧头校验和
	// f.HCS = CalcCRC16_DLT698(hcsBuf.Bytes())
	f.HCS = CalcFCS16(hcsBuf.Bytes())

	// 写入帧头校验和
	binary.Write(buf, binary.LittleEndian, f.HCS)

	// 写入APDU
	buf.Write(f.APDU)

	// 计算帧校验和
	// f.FCS = CalcCRC16_DLT698(buf.Bytes()[1:]) // 从长度域开始计算
	f.FCS = CalcFCS16(buf.Bytes()[1:]) // 从长度域开始计算

	// 写入帧校验和
	binary.Write(buf, binary.LittleEndian, f.FCS)

	// 写入帧结束符
	buf.WriteByte(f.End)

	return buf.Bytes(), nil
}

// Decode 从字节数组解码帧
func Decode(data []byte) (*Frame, error) {
	if len(data) < 7 { // 最小帧长度: 起始符(1) + 长度(2) + 控制域(1) + 地址域(至少1) + HCS(2) + FCS(2) + 结束符(1)
		return nil, errors.New("数据长度不足，无法解析为有效帧")
	}

	// 检查起始符和结束符
	if data[0] != FrameFormatNormal || data[len(data)-1] != FrameFormatEnd {
		return nil, errors.New("帧格式错误，起始符或结束符无效")
	}

	// 解析长度域
	length := binary.LittleEndian.Uint16(data[1:3])

	// 检查帧长度
	if int(length)+2 != len(data) { // +4是因为起始符(1)+结束符(1)，-3是因为FCS(2)+结束符(1)
		return nil, fmt.Errorf("帧长度不匹配，期望%d，实际%d", length+2, len(data))
	}

	// 创建帧对象
	frame := &Frame{
		Start:  data[0],
		Length: length,
		End:    data[len(data)-1],
	}

	// 解析控制域
	frame.Control = data[3]

	// 确定地址域长度
	// 地址域的最后一个字节的最高位为1，其他字节的最高位为0
	addrStart := 4
	addrEnd := addrStart
	addrEnd = addrStart + 1 + int(data[addrStart]&0x0F+1+1) // 地址域长度 = 地址域第一个字节的低4位
	if data[addrStart]&0x20>>5 == 1 {
		addrEnd++
	}

	// 提取地址域
	frame.Address = data[addrStart:addrEnd]

	// 提取帧头校验和
	hcsStart := addrEnd
	frame.HCS = binary.LittleEndian.Uint16(data[hcsStart : hcsStart+2])

	// 验证帧头校验和
	calculatedHCS := CalcFCS16(data[1:hcsStart])

	if calculatedHCS != frame.HCS {
		return nil, fmt.Errorf("帧头校验和错误，期望0x%04X，计算得到0x%04X", frame.HCS, calculatedHCS)
	}

	// 提取APDU
	apduStart := hcsStart + 2
	apduEnd := len(data) - 3
	frame.APDU = data[apduStart:apduEnd]

	// 提取帧校验和
	fcsStart := apduEnd
	frame.FCS = binary.LittleEndian.Uint16(data[fcsStart : fcsStart+2])

	// 验证帧校验和
	calculatedFCS := CalcFCS16((data[1:fcsStart]))

	if calculatedFCS != frame.FCS {
		return nil, fmt.Errorf("帧校验和错误，期望0x%04X，计算得到0x%04X", frame.FCS, calculatedFCS)
	}

	return frame, nil
}

// calcFCS 计算帧校验和 (已废弃，请使用 CalcCRC16_DLT698)
// 保留此函数是为了兼容性，新代码应使用 CalcCRC16_DLT698
// func calcFCS(data []byte) uint16 {
// 	return CalcFCS16(data)
// }

// FormatAddress 格式化地址
// 地址格式: 1字节逻辑地址 + N字节服务器地址
// 服务器地址最后一个字节的最高位为1，其他字节的最高位为0
func FormatAddress(logicalAddr byte, serverAddr []byte) []byte {
	if len(serverAddr) == 0 {
		return []byte{logicalAddr}
	}

	result := make([]byte, 1+len(serverAddr))
	result[len(serverAddr)] = logicalAddr

	// result[0] = serverAddr

	// 将服务地址倒置后复制到结果数组中
	for i := 0; i < len(serverAddr); i++ {
		result[i] = serverAddr[len(serverAddr)-1-i]
	}

	// 复制服务器地址，除了最后一个字节
	for i := 0; i < len(serverAddr)-1; i++ {
		result[i+1] = serverAddr[i] & 0x7F // 确保最高位为0
	}

	// 设置最后一个字节的最高位为1
	result[len(result)-1] = serverAddr[len(serverAddr)-1] | 0x80

	return result
}

// ParseAddress 解析地址
// 返回逻辑地址和服务器地址
func ParseAddress(address []byte) (byte, []byte) {
	if len(address) == 0 {
		return 0, nil
	}

	logicalAddr := address[0]
	if len(address) == 1 {
		return logicalAddr, nil
	}

	serverAddr := make([]byte, len(address)-1)
	for i := 0; i < len(address)-2; i++ {
		serverAddr[i] = address[i+1]
	}
	// 最后一个字节去掉最高位
	serverAddr[len(serverAddr)-1] = address[len(address)-1] & 0x7F

	return logicalAddr, serverAddr
}
