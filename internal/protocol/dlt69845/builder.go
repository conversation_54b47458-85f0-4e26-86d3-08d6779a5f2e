package dlt698

import (
	"encoding/binary"
	"errors"
	"fmt"
	"math"
)

// Builder 表示DL/T 698.45协议构建器
type Builder struct {
	serverAddr  []byte
	logicalAddr byte
	addrDomain  []byte
	invokeID    uint8
}

// NewBuilder 创建一个新的构建器
func NewBuilder(SAddrTypeSingle SAddrType, SAddrLogical0 SAddrLogical, serverAddr []byte, logicalAddr byte) *Builder {
	builder := &Builder{
		serverAddr:  serverAddr,
		logicalAddr: logicalAddr,
		invokeID:    0,
	}

	// 设置地址域
	builder.SetAddressDomain(SAddrTypeSingle, SAddrLogical0, serverAddr, logicalAddr)

	return builder
}

// GetNextInvokeID 获取下一个服务序号
func (b *Builder) GetNextInvokeID() uint8 {
	id := b.invokeID
	b.invokeID++
	if b.invokeID > 63 {
		b.invokeID = 0
	}
	return id
}

// SetAddressDomain 设置地址域
func (b *Builder) SetAddressDomain(addrType SAddrType, logicalAddr SAddrLogical, serverAddr []byte, clientAddr byte) {
	sAddrLen := len(serverAddr)
	b.addrDomain = make([]byte, 2+sAddrLen)
	b.addrDomain[0] = byte(addrType) | byte(logicalAddr) | byte(sAddrLen-1)

	// 将服务地址倒置复制到 b.addrDomain 后续位置
	for i := 0; i < len(serverAddr); i++ {
		b.addrDomain[i+1] = serverAddr[len(serverAddr)-1-i]
	}
	b.addrDomain[len(b.addrDomain)-1] = clientAddr
}

// BuildConnectRequest 构建连接请求
func (b *Builder) BuildConnectRequest() ([]byte, error) {
	// 创建APDU
	apdu := NewAPDU(
		APDUTypeConnectReq,
		b.GetNextInvokeID(),
		[]byte{
			0x00, 0x10,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
			0x04, 0x00,
			0x04, 0x00,
			0x01,
			0x04, 0x00,
			0x00, 0x00, 0x00, 0x64,
			0x00,
			0x00,
		})
	apduData, err := apdu.Encode()
	if err != nil {
		return nil, err
	}

	// 创建帧
	frame := NewFrame(ControlDirClient2Server|ControlStartClient|ControlFuncLinkManage, b.addrDomain, apduData)
	return frame.Encode()
}

// BuildDisconnectRequest 构建断开请求
func (b *Builder) BuildDisconnectRequest() ([]byte, error) {
	// 创建APDU
	apdu := NewAPDU(APDUTypeReleaseReq, b.GetNextInvokeID(), nil)
	apduData, err := apdu.Encode()
	if err != nil {
		return nil, err
	}

	// 创建帧
	frame := NewFrame(ControlDirClient2Server, b.addrDomain, apduData)
	return frame.Encode()
}

// BuildGetRequest 构建读取请求
func (b *Builder) BuildGetRequest(reqType APDUGetReqType, ois []uint16) ([]byte, error) {
	// 创建OAD列表
	oadList := make([]*OAD, len(ois))
	for i, oi := range ois {
		oadList[i] = NewOAD(oi, 2, 0) // 属性2通常是值
	}

	// 获取服务序号
	invokeID := b.GetNextInvokeID()
	var apduData []byte
	switch reqType {
	case APDUGetReqNormal:
		tmpApduData, err := EncodeGetRequestNormal(invokeID, oadList[0], nil)
		if err != nil {
			return nil, err
		}
		apduData = tmpApduData
	case APDUGetReqNormalList:
		tmpApduData, err := EncodeGetRequestNormalList(invokeID, oadList, nil)
		if err != nil {
			return nil, err
		}
		apduData = tmpApduData
	case APDUGetReqRecord:
		// 创建空的RSD和RCSD
		rsd := NewRSD_NULL()
		rcsd := NewRCSD([]CSD{})
		tmpApduData, err := EncodeGetRequestRecord(invokeID, oadList[0], rsd, rcsd, nil)
		if err != nil {
			return nil, err
		}
		apduData = tmpApduData
	default:
		return nil, errors.New("不支持的请求类型")
	}

	// 创建帧
	frame := NewFrame(ControlDirClient2Server|ControlStartClient|ControlFuncLinkManage, b.addrDomain, apduData)
	return frame.Encode()
}

// BuildGetRequestWithOADs 构建带OAD的读取请求
func (b *Builder) BuildGetRequestWithOADs(oads []*OAD) ([]byte, error) {
	// 获取服务序号
	invokeID := b.GetNextInvokeID()

	// 编码读取请求
	var apduData []byte
	if len(oads) == 1 {
		tmpApduData, err := EncodeGetRequestNormal(invokeID, oads[0], nil)
		if err != nil {
			return nil, err
		}
		apduData = tmpApduData
	} else {
		tmpApduData, err := EncodeGetRequestNormalList(invokeID, oads, nil)
		if err != nil {
			return nil, err
		}
		apduData = tmpApduData
	}

	// 创建帧
	frame := NewFrame(ControlDirClient2Server, b.addrDomain, apduData)
	return frame.Encode()
}

// BuildSetRequest 构建设置请求
func (b *Builder) BuildSetRequest(reqType APDUSetReqType, oi []uint16, attrib []byte, index []byte, value []interface{}) ([]byte, error) {
	oads := make([]*OAD, len(oi))
	for i := 0; i < len(oi); i++ {
		oads[i] = NewOAD(oi[i], attrib[i], index[i])
	}

	datas := make([][]byte, len(oi))
	for i := 0; i < len(oi); i++ {
		data, err := encodeValue(value[i])
		if err != nil {
			return nil, err
		}
		datas[i] = data
	}

	// 创建OAD-Data列表
	oadDataList := make([]OADDataPair, len(oi))
	for i := 0; i < len(oi); i++ {
		oadDataList[i] = OADDataPair{OAD: oads[i], Data: datas[i]}
	}

	// 获取服务序号
	invokeID := b.GetNextInvokeID()

	var apduData []byte
	switch reqType {
	case APDUSetReqNormal:
		tmpApduData, err := EncodeSetRequestNormal(invokeID, oadDataList[0], nil)
		if err != nil {
			return nil, err
		}
		apduData = tmpApduData
	case APDUSetReqNormalList:
		tmpApduData, err := EncodeSetRequestNormalList(invokeID, oadDataList, nil)
		if err != nil {
			return nil, err
		}
		apduData = tmpApduData
	default:
		return nil, errors.New("不支持的请求类型")
	}

	// 创建帧
	frame := NewFrame(ControlDirClient2Server|ControlStartClient|ControlFuncUserData, b.addrDomain, apduData)
	return frame.Encode()
}

// BuildActionRequest 构建操作请求
func (b *Builder) BuildActionRequest(oi uint16, methodID byte, data interface{}) ([]byte, error) {
	// 创建OAD
	oad := NewOAD(oi, methodID, 0)

	// 编码数据
	encodedData, err := encodeValue(data)
	if err != nil {
		return nil, err
	}

	// 获取服务序号
	invokeID := b.GetNextInvokeID()

	// 编码操作请求
	apduData, err := EncodeActionRequestNormal(invokeID, oad, encodedData)
	if err != nil {
		return nil, err
	}

	// 创建帧
	frame := NewFrame(ControlDirClient2Server, b.addrDomain, apduData)
	return frame.Encode()
}

// encodeValue 编码值
func encodeValue(value interface{}) ([]byte, error) {
	switch v := value.(type) {
	case nil:
		return []byte{DataTypeNull}, nil
	case bool:
		if v {
			return []byte{DataTypeBool, 1}, nil
		}
		return []byte{DataTypeBool, 0}, nil
	case uint8:
		return []byte{DataTypeUnsigned, v}, nil
	case int8:
		return []byte{DataTypeInteger, byte(v)}, nil
	case uint16:
		data := make([]byte, 3)
		data[0] = DataTypeUnsigned16
		binary.LittleEndian.PutUint16(data[1:], v)
		return data, nil
	case int16:
		data := make([]byte, 3)
		data[0] = DataTypeInteger16
		binary.LittleEndian.PutUint16(data[1:], uint16(v))
		return data, nil
	case uint32:
		data := make([]byte, 5)
		data[0] = DataTypeUnsigned32
		binary.LittleEndian.PutUint32(data[1:], v)
		return data, nil
	case int32:
		data := make([]byte, 5)
		data[0] = DataTypeInteger32
		binary.LittleEndian.PutUint32(data[1:], uint32(v))
		return data, nil
	case float32:
		data := make([]byte, 5)
		data[0] = DataTypeFloat32
		binary.LittleEndian.PutUint32(data[1:], float32ToUint32(v))
		return data, nil
	case string:
		if len(v) > 255 {
			return nil, fmt.Errorf("字符串长度超过255: %d", len(v))
		}
		data := make([]byte, 2+len(v))
		data[0] = DataTypeVisibleString
		data[1] = byte(len(v))
		copy(data[2:], v)
		return data, nil
	case []byte:
		if len(v) > 255 {
			return nil, fmt.Errorf("字节数组长度超过255: %d", len(v))
		}
		data := make([]byte, 2+len(v))
		data[0] = DataTypeOctetString
		data[1] = byte(len(v))
		copy(data[2:], v)
		return data, nil
	case *DateTimeS:
		return v.Encode(), nil
	case *OctetString:
		return v.Encode(), nil
	default:
		return nil, fmt.Errorf("不支持的数据类型: %T", value)
	}
}

// float32ToUint32 将float32转换为uint32
func float32ToUint32(f float32) uint32 {
	bits := math.Float32bits(f)
	return bits
}
