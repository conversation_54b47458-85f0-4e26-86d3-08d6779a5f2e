package dlt698

import (
	"bytes"
)

// RCSDWrapper 记录列选择描述符包装器
type RCSDWrapper struct {
	CSDs []CSD
}

// NewRCSD 创建一个新的RCSD
func NewRCSD(csds []CSD) *RCSDWrapper {
	return &RCSDWrapper{
		CSDs: csds,
	}
}

// Encode 将RCSD编码为字节数组
func (r *RCSDWrapper) Encode() []byte {
	buf := new(bytes.Buffer)

	// 写入CSD数量
	buf.WriteByte(byte(len(r.CSDs)))

	// 写入每个CSD
	for _, csd := range r.CSDs {
		switch c := csd.(type) {
		case *CSD_OAD:
			buf.WriteByte(0x00) // CSD类型为OAD
			buf.Write(c.OAD.Encode())
		case *CSD_ROAD:
			buf.WriteByte(0x01) // CSD类型为ROAD
			buf.Write(c.ROAD.Encode())
		}
	}

	return buf.Bytes()
}

// NewCSD_OAD 创建一个新的CSD_OAD
func NewCSD_OAD(oad *OAD) *CSD_OAD {
	return &CSD_OAD{
		OAD: *oad,
	}
}

// NewCSD_ROAD 创建一个新的CSD_ROAD
func NewCSD_ROAD(road *ROAD) *CSD_ROAD {
	return &CSD_ROAD{
		ROAD: *road,
	}
}

// NewROAD 创建一个新的ROAD
func NewROAD(oad *OAD, relatedOADs []*OAD) *ROAD {
	// 转换 []*OAD 为 []OAD
	oads := make([]OAD, len(relatedOADs))
	for i, o := range relatedOADs {
		oads[i] = *o
	}

	return &ROAD{
		OAD:        *oad,
		RelatedOAD: oads,
	}
}

// Encode 将ROAD编码为字节数组
func (r *ROAD) Encode() []byte {
	buf := new(bytes.Buffer)

	// 写入OAD
	buf.Write(r.OAD.Encode())

	// 写入相关OAD数量
	buf.WriteByte(byte(len(r.RelatedOAD)))

	// 写入每个相关OAD
	for _, oad := range r.RelatedOAD {
		buf.Write(oad.Encode())
	}

	return buf.Bytes()
}
