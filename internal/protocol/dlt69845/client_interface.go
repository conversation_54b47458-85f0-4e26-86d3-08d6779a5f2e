package dlt698

// ClientInterface 定义DL/T 698.45协议客户端接口
type ClientInterface interface {
	// Connect 连接设备
	Connect() error

	// Disconnect 断开连接
	Disconnect() error

	// GetData 获取数据
	GetData(ois []uint16) (map[string]interface{}, error)

	// SetData 设置数据
	SetData(oi uint16, attrib byte, index byte, value interface{}) error

	// Action 执行操作
	Action(oi uint16, methodID byte, data interface{}) error

	// IsConnected 检查是否已连接
	IsConnected() bool
}

// EncodeValue 编码值
func EncodeValue(value interface{}) ([]byte, error) {
	return encodeValue(value)
}
