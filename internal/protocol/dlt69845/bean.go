package dlt698

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"time"
)

func NewOctetString(data string) *OctetString {
	dataBuffer := make([]byte, len(data)/2)
	for i := 0; i < len(data); i += 2 {
		// 将每两个字符解析为一个字节
		fmt.Sscanf(data[i:i+2], "%02x", &dataBuffer[i/2])
	}

	octetStr := OctetString(dataBuffer)
	return &octetStr
}

func (o *OctetString) Encode() []byte {
	buf := new(bytes.Buffer)
	buf.WriteByte(DataTypeOctetString)
	buf.WriteByte(byte(len(*o)))
	buf.Write(*o)
	return buf.Bytes()
}

func DecodeOctetString(data []byte) (*OctetString, error) {
	if len(data) < 2 {
		return nil, errors.New("数据长度不足，无法解析为octet-string")
	}

	if data[0] != DataTypeOctetString {
		return nil, fmt.Errorf("数据类型错误，期望0x%02X，实际0x%02X", DataTypeOctetString, data[0])
	}

	length := int(data[1])
	if len(data) < 2+length {
		return nil, errors.New("数据长度不足，无法解析octet-string")
	}

	octetStr := OctetString(data[2 : 2+length])
	return &octetStr, nil
}

// Choice 定义基础接口
type Choice interface {
	// 用于标识具体是哪个选项
	Tag() byte
	// 编码方法
	Encode() ([]byte, error)
}

// NewDateTimeS 创建一个新的日期时间
func NewDateTimeS(year uint16, month, day, hour, minute, second byte) *DateTimeS {
	return &DateTimeS{
		Year:   LongUnsigned(year),
		Month:  Unsigned(month),
		Day:    Unsigned(day),
		Hour:   Unsigned(hour),
		Minute: Unsigned(minute),
		Second: Unsigned(second),
	}
}

// Now 返回当前日期时间
func Now() *DateTimeS {
	now := time.Now()
	return &DateTimeS{
		Year:   LongUnsigned(now.Year()),
		Month:  Unsigned(now.Month()),
		Day:    Unsigned(now.Day()),
		Hour:   Unsigned(now.Hour()),
		Minute: Unsigned(now.Minute()),
		Second: Unsigned(now.Second()),
	}
}

// Encode 将日期时间编码为字节数组
func (d *DateTimeS) Encode() []byte {
	buf := new(bytes.Buffer)
	buf.WriteByte(DataTypeDataTimeS)
	binary.Write(buf, binary.BigEndian, d.Year)
	buf.WriteByte(byte(d.Month))
	buf.WriteByte(byte(d.Day))
	buf.WriteByte(byte(d.Hour))
	buf.WriteByte(byte(d.Minute))
	buf.WriteByte(byte(d.Second))
	return buf.Bytes()
}

// DecodeDateTimeS 从字节数组解码日期时间
func DecodeDateTimeS(data []byte) (*DateTimeS, error) {
	if len(data) < 8 {
		return nil, errors.New("数据长度不足，无法解析日期时间")
	}

	if data[0] != DataTypeDateTime {
		return nil, fmt.Errorf("数据类型错误，期望0x%02X，实际0x%02X", DataTypeDateTime, data[0])
	}

	dt := &DateTimeS{
		Year:   LongUnsigned(binary.LittleEndian.Uint16(data[1:3])),
		Month:  Unsigned(data[3]),
		Day:    Unsigned(data[4]),
		Hour:   Unsigned(data[5]),
		Minute: Unsigned(data[6]),
		Second: Unsigned(data[7]),
	}

	return dt, nil
}

// ToTime 将日期时间转换为time.Time
func (d *DateTimeS) ToTime() time.Time {
	return time.Date(int(d.Year), time.Month(d.Month), int(d.Day),
		int(d.Hour), int(d.Minute), int(d.Second), 0, time.Local)
}

func NewOAD(oi uint16, attrib, index byte) *OAD {
	return &OAD{
		OI:             OI(oi),
		AttributeID:    Unsigned(attrib),
		AttributeIndex: Unsigned(index),
	}
}

func (o *OAD) Encode() []byte {
	buf := new(bytes.Buffer)
	binary.Write(buf, binary.BigEndian, uint16(o.OI))
	buf.WriteByte(byte(o.AttributeID))
	buf.WriteByte(byte(o.AttributeIndex))
	return buf.Bytes()
}

func DecodeOAD(data []byte) (*OAD, error) {
	if len(data) < 4 {
		return nil, errors.New("数据长度不足，无法解析OAD")
	}

	oad := &OAD{
		OI:             OI(binary.BigEndian.Uint16(data[0:2])),
		AttributeID:    Unsigned(data[2]),
		AttributeIndex: Unsigned(data[3]),
	}

	return oad, nil
}
