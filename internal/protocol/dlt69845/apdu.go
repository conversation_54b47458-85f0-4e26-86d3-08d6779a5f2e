package dlt698

import (
	"bytes"
	"encoding/binary"
	"errors"
)

type OPTIONAL interface {
	isOptional()
	Encode() ([]byte, error)
}

type ClientAPDU struct {
	APDUType byte
	Data     []byte
	TimeTag  OPTIONAL
}

// APDU 表示应用层协议数据单元
type APDU struct {
	Type     byte   // APDU类型
	InvokeID uint8  // 服务序号
	Data     []byte // 数据
}

// NewAPDU 创建一个新的APDU
func NewAPDU(apduType byte, invokeID uint8, data []byte) *APDU {
	return &APDU{
		Type:     apduType,
		InvokeID: invokeID,
		Data:     data,
	}
}

// Encode 将APDU编码为字节数组
func (a *APDU) Encode() ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入APDU类型
	buf.WriteByte(a.Type)

	// 写入服务序号
	binary.Write(buf, binary.LittleEndian, a.InvokeID)

	// 写入数据
	if len(a.Data) > 0 {
		buf.Write(a.Data)
	}

	return buf.Bytes(), nil
}

// DecodeAPDU 从字节数组解码APDU
func DecodeAPDU(data []byte) (*APDU, error) {
	if len(data) < 3 { // 至少需要类型和服务序号
		return nil, errors.New("数据长度不足")
	}

	apdu := &APDU{
		Type:     data[0],
		InvokeID: data[1],
	}

	if len(data) > 3 {
		apdu.Data = data[3:]
	}

	return apdu, nil
}

// EncodeGetRequestNormal 编码读取一个对象请求
func EncodeGetRequestNormal(invoke uint8, oad *OAD, timeTag []byte) ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入 OAD
	buf.Write(oad.Encode())

	// 创建 APDU
	apdu := NewAPDU(byte(APDUGetReqNormal), invoke, buf.Bytes())
	apduBuffer, err := apdu.Encode()
	if err != nil {
		return nil, err
	}
	if timeTag != nil {
		apduBuffer = append(apduBuffer, timeTag...)
	} else {
		apduBuffer = append(apduBuffer, 0x00)
	}
	apduBuffer = append([]byte{0x05}, apduBuffer...)
	return apduBuffer, nil
	// return apdu.Encode()
}

// EncodeGetRequestNormalList 编码读取多个对象请求
func EncodeGetRequestNormalList(invoke uint8, oadList []*OAD, timeTag []byte) ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入OAD数量
	buf.WriteByte(byte(len(oadList)))

	// 写入OAD列表
	for _, oad := range oadList {
		buf.Write(oad.Encode())
	}

	// 创建APDU
	apdu := NewAPDU(byte(APDUGetReqNormalList), invoke, buf.Bytes())
	apduBuffer, err := apdu.Encode()
	if err != nil {
		return nil, err
	}
	if timeTag != nil {
		apduBuffer = append(apduBuffer, timeTag...)
	} else {
		apduBuffer = append(apduBuffer, 0x00)
	}
	apduBuffer = append([]byte{0x05}, apduBuffer...)
	return apduBuffer, nil
}

// EncodeGetRequestRecord 编码读取一个记录型对象属性请求
// 参数:
// - invoke: 服务序号
// - oad: 对象属性描述符
// - rsd: 记录行选择描述符
// - rcsd: 记录列选择描述符
// - timeTag: 时间标签
func EncodeGetRequestRecord(invoke uint8, oad *OAD, rsd RSDInterface, rcsd *RCSDWrapper, timeTag []byte) ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入OAD
	buf.Write(oad.Encode())

	// 写入RSD
	if rsd != nil {
		buf.Write(rsd.Encode())
	} else {
		// 如果RSD为空，则写入空RSD
		buf.WriteByte(0)
	}

	// 写入RCSD
	if rcsd != nil {
		buf.Write(rcsd.Encode())
	} else {
		// 如果RCSD为空，则写入空RCSD（CSD数量为0）
		buf.WriteByte(0)
	}

	// 创建APDU
	apdu := NewAPDU(byte(APDUGetReqRecord), invoke, buf.Bytes())
	apduBuffer, err := apdu.Encode()
	if err != nil {
		return nil, err
	}

	// 添加时间标签
	if timeTag != nil {
		apduBuffer = append(apduBuffer, timeTag...)
	} else {
		apduBuffer = append(apduBuffer, 0x00)
	}

	// 添加APDU类型
	apduBuffer = append([]byte{0x05}, apduBuffer...)
	return apduBuffer, nil
}

// OADDataPair 表示OAD-Data对
type OADDataPair struct {
	OAD  *OAD
	Data []byte
}

// EncodeSetRequestNormal 编码设置一个对象请求
func EncodeSetRequestNormal(invoke uint8, oadData OADDataPair, timeTag []byte) ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入PIID-ACD
	// buf.WriteByte(0x00) // 无附加数据

	// 写入OAD-Data列表
	buf.Write(oadData.OAD.Encode())
	buf.Write(oadData.Data)

	// 创建APDU
	apdu := NewAPDU(byte(APDUSetReqNormal), invoke, buf.Bytes())
	apduData, err := apdu.Encode()
	if err != nil {
		return nil, err
	}
	if timeTag != nil {
		apduData = append(apduData, timeTag...)
	} else {
		apduData = append(apduData, 0x00)
	}
	apduData = append([]byte{0x06}, apduData...)
	return apduData, nil
}

// EncodeSetRequestNormalList 编码设置多个对象请求
func EncodeSetRequestNormalList(invoke uint8, oadDataList []OADDataPair, timeTag []byte) ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入PIID-ACD
	// buf.WriteByte(0x00) // 无附加数据

	// 写入OAD-Data数量
	buf.WriteByte(byte(len(oadDataList)))

	// 写入OAD-Data列表
	for _, item := range oadDataList {
		buf.Write(item.OAD.Encode())
		buf.Write(item.Data)
	}

	// 创建APDU
	apdu := NewAPDU(byte(APDUSetReqNormalList), invoke, buf.Bytes())
	apduData, err := apdu.Encode()
	if err != nil {
		return nil, err
	}
	if timeTag != nil {
		apduData = append(apduData, timeTag...)
	} else {
		apduData = append(apduData, 0x00)
	}
	apduData = append([]byte{0x06}, apduData...)
	return apduData, nil
}

// EncodeActionRequestNormal 编码操作请求
func EncodeActionRequestNormal(invoke uint8, oad *OAD, data []byte) ([]byte, error) {
	buf := new(bytes.Buffer)

	// 写入PIID-ACD
	buf.WriteByte(0x00) // 无附加数据

	// 写入OAD
	buf.Write(oad.Encode())

	// 写入数据
	buf.Write(data)

	// 创建APDU
	apdu := NewAPDU(byte(APDUTypeActionReq), invoke, buf.Bytes())
	return apdu.Encode()
}
