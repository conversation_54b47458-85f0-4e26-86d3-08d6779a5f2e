package dlt698

import (
	"bytes"
)

type CHOICE interface {
	IsCHOICE()
	Encode() ([]byte, error)
	Decode(data []byte) error
}

// Array 数组类型
type Array struct {
	Elements []interface{}
}

// NewArray 创建一个新的数组
func NewArray(elements []interface{}) *Array {
	return &Array{
		Elements: elements,
	}
}

func (a *Array) IsCHOICE() {}

// Encode 将数组编码为字节数组
func (a *Array) Encode() []byte {
	buf := new(bytes.Buffer)

	// 写入元素数量
	buf.WriteByte(byte(len(a.Elements)))

	// 写入每个元素
	for _, element := range a.Elements {
		encoded, err := encodeValue(element)
		if err != nil {
			continue
		}
		buf.Write(encoded)
	}

	return buf.Bytes()
}

func (a *Array) IsCSD() {}
