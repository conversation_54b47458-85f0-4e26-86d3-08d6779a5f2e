package dlt698

// DL/T 698.45 协议常量定义

// 帧格式标识
const (
	FrameFormatNormal byte = 0x68 // 正常帧起始符
	FrameFormatEnd    byte = 0x16 // 帧结束符
)

// 控制域C
const (
	// 传输方向位
	ControlDirClient2Server byte = 0x00 // 客户端发往服务器
	ControlDirServer2Client byte = 0x80 // 服务器发往客户端

	// 启动标志位
	ControlStartServer byte = 0x00 // 服务器发起
	ControlStartClient byte = 0x40 // 客户端发起

	// 分帧标志
	ControlFrameSingle  byte = 0x00 // 完整 APDU
	ControlFrameSnippet byte = 0x20 // 片段 APDU

	// 扰码标志 SC
	ControlScNone byte = 0x00 // 无扰码
	ControlSc     byte = 0x08 // 扰码

	// 功能码
	ControlFuncLinkManage byte = 0x01 // 链路管理 - 链路连接管理（登录、心跳、退出登录）
	ControlFuncUserData   byte = 0x03 // 用户数据 - 应用连接管理及数据交换服务
)

// 地址域
type SAddrType byte
type SAddrLogical byte

const (
	// 服务器地址类型
	SAddrTypeSingle    SAddrType = 0x00 // 单地址
	SAddrTypeWildcard  SAddrType = 0x40 // 通配地址
	SAddrTypeGroup     SAddrType = 0x80 // 组地址
	SAddrTypeBroadcast SAddrType = 0xC0 // 广播地址

	// 服务器逻辑地址
	SAddrLogicalNone SAddrLogical = 0x00 // 无逻辑地址
	SAddrLogical0    SAddrLogical = 0x00 // 逻辑地址0
	SAddrLogical1    SAddrLogical = 0x10 // 逻辑地址1
)

// APDU类型
const (
	// 预连接数据单元
	APDUTypeLinkReq  byte = 0x01 // 预连接请求
	APDUTypeLinkResp byte = 0x81 // 预连接响应

	// 客户机应用层数据单元
	APDUTypeConnectReq byte = 0x02 // 建立应用连接请求
	APDUTypeReleaseReq byte = 0x03 // 断开应用连接请求
	APDUTypeGetReq     byte = 0x05 // 读取请求
	APDUTypeSetReq     byte = 0x06 // 设置请求
	APDUTypeActionReq  byte = 0x07 // 操作请求
	APDUTypeReportResp byte = 0x08 // 上报应答
	APDUTypeProxyReq   byte = 0x09 // 代理请求

	// 服务器应用层数据单元
	APDUTypeConnectResp byte = 0x82 // 建立应用连接响应
	APDUTypeReleaseResp byte = 0x83 // 断开应用连接响应
	APDUTypeGetResp     byte = 0x85 // 读取响应
	APDUTypeSetResp     byte = 0x86 // 设置响应
	APDUTypeActionResp  byte = 0x87 // 操作响应
	APDUTypeReportReq   byte = 0x88 // 上报响应
	APDUTypeProxyResp   byte = 0x89 // 代理响应

	// 异常相应
	APDUTypeError byte = 0xEE // 异常响应
)

// APDU请求类型
type APDUReqType byte
type APDUGetReqType byte
type APDUSetReqType byte
type APDUActionReqType byte

const (
	// 预连接请求
	APDULinkReqLogin APDUReqType = 0x00 // 登录
	APDULinkReqHeart APDUReqType = 0x01 // 心跳
	APDULinkReqExit  APDUReqType = 0x02 // 退出登录

	// 读取请求
	APDUGetReqNormal     APDUGetReqType = 0x01 // 请求读取一个对象属性
	APDUGetReqNormalList APDUGetReqType = 0x02 // 请求读取若干个对象属性
	APDUGetReqRecord     APDUGetReqType = 0x03 // 请求读取一个记录型对象属性
	APDUGetReqRecordList APDUGetReqType = 0x04 // 请求读取若干个记录型对象属性
	APDUGetReqNext       APDUGetReqType = 0x05 // 请求读取分帧传输的下一帧
	APDUGetReqMD5        APDUGetReqType = 0x06 // 请求读取一个对象属性的 MD5 值
	APDUGetReqSimplify   APDUGetReqType = 0x17 // 读取一个精简的记录型对象属性请求

	// 设置请求
	APDUSetReqNormal        APDUSetReqType = 0x01 // 请求设置一个对象属性
	APDUSetReqNormalList    APDUSetReqType = 0x02 // 请求设置若干个对象属性
	APDUSetReqGetNormalList APDUSetReqType = 0x03 // 请求设置后读取若干个对象属性

	// 操作请求
	// 请求操作一个对象方法                 [1] ActionRequestNormal，
	// 请求操作若干个对象方法                   [2] ActionRequestNormalList，
	// 请求操作若干个对象方法后读取若干个对象属性 [3] ActionThenGetRequestNormalList
	APDUActionReqNormal     APDUActionReqType = 0x01
	APDUActionReqNormalList APDUActionReqType = 0x02
	APDUActionReqGetList    APDUActionReqType = 0x03
)

// 数据类型
const (
	DataTypeNull          byte = 0x00 // 空
	DataTypeArray         byte = 0x01 // 数组
	DataTypeStruct        byte = 0x02 // 结构
	DataTypeBool          byte = 0x03 // 布尔
	DataTypeBitString     byte = 0x04 // 比特串
	DataTypeDoubleLong    byte = 0x05 // 双长整型
	DataTypeDoubleLongU   byte = 0x06 // 双长无符号
	DataTypeOctetString   byte = 0x09 // 八位字节串
	DataTypeVisibleString byte = 0x0A // 可见字符串
	DataTypeUTF8String    byte = 0x0C // UTF-8字符串
	DataTypeInteger       byte = 0x0F // 整数
	DataTypeInteger8      byte = 0x10 // 8位整数
	DataTypeInteger16     byte = 0x11 // 16位整数
	DataTypeInteger32     byte = 0x12 // 32位整数
	DataTypeInteger64     byte = 0x13 // 64位整数
	DataTypeUnsigned      byte = 0x14 // 无符号整数
	DataTypeUnsigned8     byte = 0x15 // 8位无符号整数
	DataTypeUnsigned16    byte = 0x16 // 16位无符号整数
	DataTypeUnsigned32    byte = 0x17 // 32位无符号整数
	DataTypeUnsigned64    byte = 0x18 // 64位无符号整数
	DataTypeEnum          byte = 0x1F // 枚举
	DataTypeFloat32       byte = 0x23 // 32位浮点数
	DataTypeFloat64       byte = 0x24 // 64位浮点数
	DataTypeDateTime      byte = 0x19 // 日期时间
	DataTypeDate          byte = 0x1A // 日期
	DataTypeTime          byte = 0x1B // 时间
	DataTypeDataTimeS     byte = 0x1C // 精简日期时间
	DataTypeOI            byte = 0x50 // 对象标识符
	DataTypeOAD           byte = 0x51 // 对象属性描述符
	DataTypeROAD          byte = 0x52 // 相对对象属性描述符
	DataTypeTI            byte = 0x54 // 时间间隔
	DataTypeTSA           byte = 0x55 // 传输服务访问点地址
	DataTypeMAC           byte = 0x56 // MAC地址
	DataTypeRN            byte = 0x57 // 随机数
	DataTypeRegion        byte = 0x58 // 区域
	DataTypeScaler_Unit   byte = 0x59 // 换算单位
	DataTypeRSD           byte = 0x5A // 响应状态描述符
	DataTypeCSD           byte = 0x5B // 电能表集合描述符
	DataTypeMS            byte = 0x5C // 制造商规范
	DataTypeSID           byte = 0x5D // 安全标识
	DataTypeOMS           byte = 0x5E // 对象方法说明
	DataTypeOI_Block      byte = 0x60 // OI块
	DataTypeOAD_Block     byte = 0x61 // OAD块
	DataTypeROAD_Block    byte = 0x62 // ROAD块
	DataTypeError         byte = 0xEE // 错误类型
)

// 错误码
const (
	ErrorSuccess                 byte = 0x00 // 成功
	ErrorHardwareFault           byte = 0x01 // 硬件失效
	ErrorTemporaryFault          byte = 0x02 // 暂时失效
	ErrorReadWriteDenied         byte = 0x03 // 拒绝读写
	ErrorObjectUndefined         byte = 0x04 // 对象未定义
	ErrorObjectClassInconsistent byte = 0x05 // 对象接口类不一致
	ErrorObjectUnavailable       byte = 0x06 // 对象不存在
	ErrorTypeUnmatched           byte = 0x07 // 类型不匹配
	ErrorScopeOutOfBounds        byte = 0x08 // 越界
	ErrorDataOutOfBounds         byte = 0x09 // 数据块不可用
	ErrorObjectMethodUnmatched   byte = 0x0A // 对象方法不匹配
	ErrorObjectMethodUnavailable byte = 0x0B // 对象方法不可用
	ErrorOtherReason             byte = 0x0F // 其他原因
)

// 对象标识符(OI)
const (
	OI_CommonInfo             uint16 = 0x4000 // 公共信息
	OI_CurrentTransformerInfo uint16 = 0x4001 // 电流互感器参数
	OI_VoltageTransformerInfo uint16 = 0x4002 // 电压互感器参数
	OI_MeterInfo              uint16 = 0x4003 // 电能表参数
	OI_FactoryInfo            uint16 = 0x4004 // 出厂信息
	OI_StateInfo              uint16 = 0x4005 // 状态信息
	OI_ActivePower            uint16 = 0x2000 // 有功功率
	OI_ReactivePower          uint16 = 0x2001 // 无功功率
	OI_PowerFactor            uint16 = 0x2002 // 功率因数
	OI_CurrentValue           uint16 = 0x2003 // 电流值
	OI_VoltageValue           uint16 = 0x2004 // 电压值
	OI_ActiveEnergy           uint16 = 0x0000 // 有功电能
	OI_ReactiveEnergy         uint16 = 0x0001 // 无功电能
)
