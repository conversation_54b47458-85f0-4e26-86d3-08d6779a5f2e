/**
 * DL/T 698.45 通信协议服务
 * DL/T 698.45 的通信协议组织及解析服务
 */
syntax = "proto3";

package protocol;

option go_package = "./protocol";

/**
 * 通信协议服务网关
 * 对检测服务提供协议报文的组织及解析支持
 */
service DLT69845ProtSvc {
    /**
     * 获取服务信息 Get Service Info
     * 参数
     *   - 无
     * 返回
     *   - services: 服务信息
     */
    rpc GetSvcInfo(GetSvcInfoReq) returns (GetSvcInfoResp);

    /**
     * 协议数据域组织 Protocol Data Domain Organization
     * 参数
     *   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
     *             例 Modbus 需要寄存器类型、地址、数据等
     *             例 DL/T 698.45 需要数据结构类型、地址、数据等
     * 返回
     *   - datadomain: 组织后的数据, 字符串格式
     */
    rpc ProtDDO(CPDOReq) returns (CPDOResp);

    /**
     * 协议数据域解析 Protocol Data Domain Parsing
     * 参数
     *   - datadomain: 待解析的数据, 字符串格式
     * 返回
     *   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
     */
    rpc ProtDDP(CPDReq) returns (CPDResp);

    /**
     * 协议组织 Protocol Organization
     * 参数
     *   - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
     *             例 Modbus 需要寄存器类型、地址、数据等
     *             例 DL/T 698.45 需要数据结构类型、地址、数据等
     * 返回
     *   - frame: 组织后的数据, 字符串格式
     */
    rpc ProtO(CPOReq) returns (CPOResp);

    /**
     * 协议解析 Protocol Parsing
     * 参数
     *   - frame: 待解析的数据, 字符串格式
     * 返回
     *   - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
     */
    rpc ProtP(CPPReq) returns (CPPResp);
}

// 服务信息
message ProtServiceInfo {
    // 协议服务名称，每个协议需求分析时确定，不可重复（可用协议类型的 string 格式进行替代）
    string name = 1;
    // 协议版本，用于协议升级时的兼容性判断
    string version = 2;
    // 协议描述，用于展示给用户，描述协议的功能、用途等信息
    string description = 3;
}

// 服务信息获取请求
message GetSvcInfoReq {}

// 服务信息获取响应
message GetSvcInfoResp {
    // 协议服务信息
    ProtServiceInfo services = 1;
}


/** 通讯协议数据域组织请求 Communication Protocol DataDomain Organization
 * DL/T 698.45 协议为例：
 * {
 *     "type": 0x0100,      // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
 *     "addr": "",          // 地址
 *     "data": { 
 *         ... // 根据请求类型不同，传递不同的参数
 *     },
 *     "timetag": 0        // 时间标签，0 无时间标签（默认无时间标签，可不传）
 * }
*/
message CPDOReq {
    // 组织参数, JSON 格式
    string params = 1;
}

// 协议数据域组织响应
message CPDOResp {
    // 组织后的数据, 字符串格式
    string datadomain = 1;
}


/** 通讯协议组织请求 Communication Protocol Organization
 * DL/T 698.45 协议为例：
 * 1. 先组织的数据域（APDU）
 * "params": {
 *     "datadomain": ""
 * }
 * 2. 传入不同数据直接组织协议报文
 * "params": {
 *     "type": 0x0100,  // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
 *     "addr": "",      // 地址
 *     "data": { 
 *         ... // 根据请求类型不同，传递不同的参数
 *     },
 *     "timetag": 0    // 时间标签，0 无时间标签（默认无时间标签，可不传）
 * }
*/
message CPOReq {
    // 组织参数, JSON 格式
    string params = 1;
}

// 协议组织响应
message CPOResp {
    // 组织后的数据, 字符串格式
    string frame = 1;
}

// 通讯协议解析请求 Communication Protocol Parsing
message CPPReq {
    // 待解析的数据, 字符串格式
    string frame = 1;
}

// 协议解析响应
message CPPResp {
    // 解析后的数据, JSON 格式
    string data = 1;
}

// 通讯协议数据域解析请求
message CPDReq {
    // 待解析的数据, 字符串格式
    string datadomain = 1;
}

// 协议数据域解析响应
message CPDResp {
    // 解析后的数据, JSON 格式
    string data = 1;
}