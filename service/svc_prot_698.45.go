package service

import (
	"context"

	proto "tp.service/service/bean/api/protocol"
)

type DLT69845ProtSvc struct {
	proto.UnimplementedDLT69845ProtSvcServer
}

func NewDLT69845ProtSvcServer() *DLT69845ProtSvc {
	return &DLT69845ProtSvc{}
}

func (s *DLT69845ProtSvc) GetSvcInfo(ctx context.Context, req *proto.GetSvcInfoReq) (*proto.GetSvcInfoResp, error) {
	return &proto.GetSvcInfoResp{
		Services: &proto.ProtServiceInfo{
			Name:        "DL/T 698.45",
			Version:     "1.0.0",
			Description: "DL/T 698.45 通信协议服务",
		},
	}, nil
}
