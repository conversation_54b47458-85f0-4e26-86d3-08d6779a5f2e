package service

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	proto "tp.service/service/bean/api/protocol"
)

// TestProtocolGateway 测试协议网关基本功能
func TestProtocolGateway(t *testing.T) {
	// 创建网关
	config := &GatewayConfig{
		ServiceDiscovery: map[string]ServiceConfig{
			"DL/T 698.45": {
				Address:    "localhost:50051",
				Enabled:    true,
				Type:       "local", // 使用本地服务进行测试
				RetryCount: 3,
			},
		},
		HealthCheckInterval: 30 * time.Second,
		ConnectionTimeout:   5 * time.Second,
	}
	
	gateway := NewProtocolGateway(config)
	if gateway == nil {
		t.Fatal("Failed to create protocol gateway")
	}

	// 检查服务是否注册
	services := gateway.ListServices()
	if len(services) == 0 {
		t.<PERSON>al("No services registered")
	}

	found := false
	for _, service := range services {
		if service == "DL/T 698.45" {
			found = true
			break
		}
	}
	if !found {
		t.Fatal("DL/T 698.45 service not found")
	}

	t.Logf("Registered services: %v", services)
}

// TestProtocolServer 测试协议服务器
func TestProtocolServer(t *testing.T) {
	// 创建协议服务器 (传入 nil 作为数据库连接，仅用于测试)
	server := NewProtocolServer(nil)
	if server == nil {
		t.Fatal("Failed to create protocol server")
	}

	ctx := context.Background()

	// 测试获取服务信息
	t.Run("GetServiceInfo", func(t *testing.T) {
		// 获取所有服务
		resp, err := server.GetServiceInfo(ctx, &proto.GetServiceInfoRequest{Name: ""})
		if err != nil {
			t.Fatalf("Failed to get all services: %v", err)
		}
		if len(resp.Services) == 0 {
			t.Fatal("No services returned")
		}
		t.Logf("Found %d services", len(resp.Services))

		// 获取特定服务
		resp, err = server.GetServiceInfo(ctx, &proto.GetServiceInfoRequest{Name: "DL/T 698.45"})
		if err != nil {
			t.Fatalf("Failed to get DL/T 698.45 service: %v", err)
		}
		if len(resp.Services) != 1 {
			t.Fatalf("Expected 1 service, got %d", len(resp.Services))
		}
		if resp.Services[0].Name != "DL/T 698.45" {
			t.Fatalf("Expected DL/T 698.45, got %s", resp.Services[0].Name)
		}
		t.Logf("DL/T 698.45 service: %s v%s", resp.Services[0].Name, resp.Services[0].Version)
	})

	// 测试协议数据域组织
	t.Run("ProtocolDataDomainOrganization", func(t *testing.T) {
		params := map[string]interface{}{
			"type":    0x0100,
			"addr":    "123456789012",
			"data":    map[string]interface{}{"test": "value"},
			"timetag": 0,
		}
		paramsJSON, _ := json.Marshal(params)

		resp, err := server.ProtocolDataDomainOrganization(ctx, &proto.CPDORequest{
			Name:   "DL/T 698.45",
			Params: string(paramsJSON),
		})
		if err != nil {
			t.Fatalf("Failed to organize data domain: %v", err)
		}
		if resp.Datadomain == "" {
			t.Fatal("Empty data domain returned")
		}
		t.Logf("Data domain: %s", resp.Datadomain)
	})

	// 测试协议组织
	t.Run("ProtocolOrganization", func(t *testing.T) {
		params := `{"type": 256, "addr": "123456789012", "data": {"test": "value"}}`
		
		resp, err := server.ProtocolOrganization(ctx, &proto.CPORequest{
			Name:   "DL/T 698.45",
			Params: params,
		})
		if err != nil {
			t.Fatalf("Failed to organize protocol: %v", err)
		}
		if resp.Frame == "" {
			t.Fatal("Empty frame returned")
		}
		t.Logf("Protocol frame: %s", resp.Frame)
	})

	// 测试协议解析
	t.Run("ProtocolParsing", func(t *testing.T) {
		testFrame := "68123456789012681000010203040516"
		
		resp, err := server.ProtocolParsing(ctx, &proto.CPPRequest{
			Name:  "DL/T 698.45",
			Frame: testFrame,
		})
		if err != nil {
			t.Fatalf("Failed to parse protocol: %v", err)
		}
		if resp.Data == "" {
			t.Fatal("Empty data returned")
		}
		t.Logf("Parsed data: %s", resp.Data)
	})

	// 测试协议数据域解析
	t.Run("ProtocolDataDomainParsing", func(t *testing.T) {
		testDataDomain := "010203040516"
		
		resp, err := server.ProtocolDataDomainParsing(ctx, &proto.CPDRequest{
			Name:       "DL/T 698.45",
			Datadomain: testDataDomain,
		})
		if err != nil {
			t.Fatalf("Failed to parse data domain: %v", err)
		}
		if resp.Data == "" {
			t.Fatal("Empty data returned")
		}
		t.Logf("Parsed data domain: %s", resp.Data)
	})

	// 测试不存在的服务
	t.Run("NonExistentService", func(t *testing.T) {
		_, err := server.GetServiceInfo(ctx, &proto.GetServiceInfoRequest{Name: "NonExistent"})
		if err == nil {
			t.Fatal("Expected error for non-existent service")
		}
		t.Logf("Expected error: %v", err)
	})
}

// TestCustomProtocolClient 测试自定义协议客户端
func TestCustomProtocolClient(t *testing.T) {
	client := CreateCustomProtocolClient("Test Protocol", "1.0.0", "测试协议")
	
	ctx := context.Background()

	// 测试获取服务信息
	info, err := client.GetServiceInfo(ctx)
	if err != nil {
		t.Fatalf("Failed to get service info: %v", err)
	}
	if info.Name != "Test Protocol" {
		t.Fatalf("Expected 'Test Protocol', got '%s'", info.Name)
	}
	t.Logf("Custom protocol info: %s v%s - %s", info.Name, info.Version, info.Description)

	// 测试协议组织
	frame, err := client.ProtocolOrganization(ctx, `{"test": "data"}`)
	if err != nil {
		t.Fatalf("Failed to organize protocol: %v", err)
	}
	t.Logf("Custom protocol frame: %s", frame)

	// 测试协议解析
	data, err := client.ProtocolParsing(ctx, "test_frame")
	if err != nil {
		t.Fatalf("Failed to parse protocol: %v", err)
	}
	t.Logf("Custom protocol parsed data: %s", data)
}

// TestGatewayManagement 测试网关管理功能
func TestGatewayManagement(t *testing.T) {
	gateway := NewProtocolGateway(nil)
	
	// 添加自定义服务
	customClient := CreateCustomProtocolClient("Management Test", "1.0.0", "管理测试协议")
	gateway.AddService("Management Test", customClient)
	
	// 检查服务是否添加
	services := gateway.ListServices()
	found := false
	for _, service := range services {
		if service == "Management Test" {
			found = true
			break
		}
	}
	if !found {
		t.Fatal("Custom service not found after adding")
	}
	t.Logf("Services after adding: %v", services)
	
	// 获取服务状态
	status := gateway.GetServiceStatus()
	if len(status) == 0 {
		t.Fatal("No service status returned")
	}
	t.Logf("Service status: %v", status)
	
	// 移除服务
	gateway.RemoveService("Management Test")
	services = gateway.ListServices()
	for _, service := range services {
		if service == "Management Test" {
			t.Fatal("Custom service still found after removal")
		}
	}
	t.Logf("Services after removal: %v", services)
}

// BenchmarkProtocolOrganization 协议组织性能测试
func BenchmarkProtocolOrganization(b *testing.B) {
	server := NewProtocolServer(nil)
	ctx := context.Background()
	params := `{"type": 256, "addr": "123456789012", "data": {"test": "value"}}`
	
	req := &proto.CPORequest{
		Name:   "DL/T 698.45",
		Params: params,
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := server.ProtocolOrganization(ctx, req)
		if err != nil {
			b.Fatalf("Failed to organize protocol: %v", err)
		}
	}
}

// BenchmarkProtocolParsing 协议解析性能测试
func BenchmarkProtocolParsing(b *testing.B) {
	server := NewProtocolServer(nil)
	ctx := context.Background()
	testFrame := "68123456789012681000010203040516"
	
	req := &proto.CPPRequest{
		Name:  "DL/T 698.45",
		Frame: testFrame,
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := server.ProtocolParsing(ctx, req)
		if err != nil {
			b.Fatalf("Failed to parse protocol: %v", err)
		}
	}
}
