/**
 * 协议服务网关
 */
package service

import (
	"context"
	"fmt"

	"google.golang.org/grpc"
	"gorm.io/gorm"
	proto "tp.service/service/bean/api/protocol"
)

type ProtocolServer struct {
	proto.UnimplementedBaseProtocolServiceServer
	svcDLT69845 proto.DLT69845ProtSvcClient
	db          *gorm.DB
}

func NewProtocolServer(db *gorm.DB) *ProtocolServer {
	// 初始化所有服务连接
	svcDLT69845, err := grpc.Dial("dlt698.45-service:50051", grpc.WithInsecure())
	if err != nil {
		panic(err)
	}

	return &ProtocolServer{
		svcDLT69845: proto.NewDLT69845ProtSvcClient(svcDLT69845),
		db:          db,
	}
}

// 获取服务信息
func (s *ProtocolServer) GetServiceInfo(ctx context.Context, req *proto.GetServiceInfoRequest) (*proto.GetServiceInfoResponse, error) {
	// 获取所有服务信息
	var services []*proto.ProtServiceInfo
	if req.Name == "" {
		// DL/T 698.45
		resp, err := s.svcDLT69845.GetSvcInfo(ctx, &proto.GetSvcInfoReq{})
		if err != nil {
			return nil, err
		}
		services = append(services, resp.Services)

		return &proto.GetServiceInfoResponse{
			Services: services,
		}, nil
	}

	switch req.Name {
	case "DL/T 698.45":
		resp, err := s.svcDLT69845.GetSvcInfo(ctx, &proto.GetSvcInfoReq{})
		if err != nil {
			return nil, err
		}
		return &proto.GetServiceInfoResponse{
			Services: []*proto.ProtServiceInfo{resp.Services},
		}, nil
	default:
		return nil, fmt.Errorf("service not found: %s", req.Name)
	}
}
