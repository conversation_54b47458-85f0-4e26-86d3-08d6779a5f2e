/**
 * 协议服务网关管理器
 * 提供协议服务的注册、发现和管理功能
 */
package service

import (
	"context"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	proto "tp.service/service/bean/api/protocol"
)

// ProtocolGateway 协议服务网关
type ProtocolGateway struct {
	registry *ProtocolServiceRegistry
	config   *GatewayConfig
}

// GatewayConfig 网关配置
type GatewayConfig struct {
	// 服务发现配置
	ServiceDiscovery map[string]ServiceConfig `json:"service_discovery"`
	// 健康检查间隔
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	// 连接超时
	ConnectionTimeout time.Duration `json:"connection_timeout"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	// 服务地址
	Address string `json:"address"`
	// 是否启用
	Enabled bool `json:"enabled"`
	// 服务类型 (remote/local)
	Type string `json:"type"`
	// 重试次数
	RetryCount int `json:"retry_count"`
}

// NewProtocolGateway 创建协议服务网关
func NewProtocolGateway(config *GatewayConfig) *ProtocolGateway {
	if config == nil {
		config = getDefaultConfig()
	}

	gateway := &ProtocolGateway{
		registry: &ProtocolServiceRegistry{
			services: make(map[string]ProtocolServiceClient),
		},
		config: config,
	}

	// 初始化服务
	gateway.initializeServices()

	// 启动健康检查
	go gateway.startHealthCheck()

	return gateway
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *GatewayConfig {
	return &GatewayConfig{
		ServiceDiscovery: map[string]ServiceConfig{
			"DL/T 698.45": {
				Address:    "dlt698.45-service:50051",
				Enabled:    true,
				Type:       "remote",
				RetryCount: 3,
			},
			"Modbus": {
				Address:    "modbus-service:50052",
				Enabled:    false,
				Type:       "remote",
				RetryCount: 3,
			},
			"DL/T 645": {
				Address:    "dlt645-service:50053",
				Enabled:    false,
				Type:       "remote",
				RetryCount: 3,
			},
		},
		HealthCheckInterval: 30 * time.Second,
		ConnectionTimeout:   5 * time.Second,
	}
}

// initializeServices 初始化服务
func (g *ProtocolGateway) initializeServices() {
	for serviceName, config := range g.config.ServiceDiscovery {
		if !config.Enabled {
			continue
		}

		switch serviceName {
		case "DL/T 698.45":
			g.registerDLT69845Service(config)
		case "Modbus":
			g.registerModbusService(config)
		case "DL/T 645":
			g.registerDLT645Service(config)
		default:
			log.Printf("Unknown service: %s", serviceName)
		}
	}
}

// registerDLT69845Service 注册 DL/T 698.45 服务
func (g *ProtocolGateway) registerDLT69845Service(config ServiceConfig) {
	if config.Type == "remote" {
		// 尝试连接远程服务
		ctx, cancel := context.WithTimeout(context.Background(), g.config.ConnectionTimeout)
		defer cancel()

		conn, err := grpc.NewClient(config.Address,
			grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			log.Printf("Failed to connect to remote DL/T 698.45 service, using local: %v", err)
			g.registerLocalDLT69845Service()
			return
		}

		// 测试连接
		client := proto.NewDLT69845ProtSvcClient(conn)
		_, err = client.GetSvcInfo(ctx, &proto.GetSvcInfoReq{})
		if err != nil {
			log.Printf("Remote DL/T 698.45 service not responding, using local: %v", err)
			conn.Close()
			g.registerLocalDLT69845Service()
			return
		}

		g.registry.RegisterService("DL/T 698.45", &DLT69845ServiceClient{client: client})
		log.Printf("Registered remote DL/T 698.45 service at %s", config.Address)
	} else {
		g.registerLocalDLT69845Service()
	}
}

// registerLocalDLT69845Service 注册本地 DL/T 698.45 服务
func (g *ProtocolGateway) registerLocalDLT69845Service() {
	localService := NewDLT69845ProtSvcServer()
	g.registry.RegisterService("DL/T 698.45", &LocalDLT69845ServiceClient{service: localService})
	log.Printf("Registered local DL/T 698.45 service")
}

// registerModbusService 注册 Modbus 服务 (示例)
func (g *ProtocolGateway) registerModbusService(config ServiceConfig) {
	// TODO: 实现 Modbus 服务注册
	log.Printf("Modbus service registration not implemented yet")
}

// registerDLT645Service 注册 DL/T 645 服务 (示例)
func (g *ProtocolGateway) registerDLT645Service(config ServiceConfig) {
	// TODO: 实现 DL/T 645 服务注册
	log.Printf("DL/T 645 service registration not implemented yet")
}

// startHealthCheck 启动健康检查
func (g *ProtocolGateway) startHealthCheck() {
	ticker := time.NewTicker(g.config.HealthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		g.performHealthCheck()
	}
}

// performHealthCheck 执行健康检查
func (g *ProtocolGateway) performHealthCheck() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	services := g.registry.GetAllServices()
	for serviceName, client := range services {
		_, err := client.GetServiceInfo(ctx)
		if err != nil {
			log.Printf("Health check failed for service %s: %v", serviceName, err)
			// TODO: 实现服务重连逻辑
		}
	}
}

// GetRegistry 获取服务注册表
func (g *ProtocolGateway) GetRegistry() *ProtocolServiceRegistry {
	return g.registry
}

// AddService 动态添加服务
func (g *ProtocolGateway) AddService(name string, client ProtocolServiceClient) {
	g.registry.RegisterService(name, client)
	log.Printf("Dynamically added service: %s", name)
}

// RemoveService 动态移除服务
func (g *ProtocolGateway) RemoveService(name string) {
	g.registry.mutex.Lock()
	defer g.registry.mutex.Unlock()

	if _, exists := g.registry.services[name]; exists {
		delete(g.registry.services, name)
		log.Printf("Removed service: %s", name)
	}
}

// GetServiceStatus 获取服务状态
func (g *ProtocolGateway) GetServiceStatus() map[string]bool {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	status := make(map[string]bool)
	services := g.registry.GetAllServices()

	for serviceName, client := range services {
		_, err := client.GetServiceInfo(ctx)
		status[serviceName] = err == nil
	}

	return status
}

// ListServices 列出所有服务
func (g *ProtocolGateway) ListServices() []string {
	g.registry.mutex.RLock()
	defer g.registry.mutex.RUnlock()

	var services []string
	for name := range g.registry.services {
		services = append(services, name)
	}

	return services
}
